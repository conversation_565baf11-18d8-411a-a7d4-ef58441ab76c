package com.hnguigu.springcloud.controller;

import com.hnguigu.springcloud.domain.entity.Goods;
import com.hnguigu.springcloud.service.GoodsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/goods")
public class GoodsController {

    @Autowired
    private GoodsService goodsService;

    @GetMapping("/{id}")
    public Goods findGoodsById(@PathVariable Long id) {
//        int result = 1 / 0;

        if (id == 1) {
            throw new IllegalArgumentException("商品信息有误");
        }


        return this.goodsService.findGoodsById(id);
    }

    /*@GetMapping("/{id}")
    public Goods findGoodsById(@PathVariable Long id,
                               @RequestHeader(value = "Authorization") String token) {
        log.info("token:{}", token);
        return this.goodsService.findGoodsById(id);
    }*/
}

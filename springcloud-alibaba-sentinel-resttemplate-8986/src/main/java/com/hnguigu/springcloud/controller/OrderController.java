package com.hnguigu.springcloud.controller;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.hnguigu.springcloud.domain.entity.Order;
import com.hnguigu.springcloud.handler.HnguiguSentinelHandler;
import com.hnguigu.springcloud.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/orders")
@Slf4j
public class OrderController {

    @Autowired
    private OrderService orderService;

    @PostMapping("/{goodsId}")
    public String createOrder(@RequestBody Order order, @PathVariable Long goodsId) {
        this.orderService.createOrder(order, goodsId);
        return "success";
    }

    /**
     * blockHandler：处理流控
     * fallback：处理异常，走兜底方案（降级）
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public String findOrderById(@PathVariable Long id) {
//        int result = 1 / 0;
        System.out.println("findOrderById");
        return "success";
    }


}
